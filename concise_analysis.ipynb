#%% md
# Concise CSV Data Analysis
# 
# This notebook provides a streamlined approach to:
# - Load multiple CSV files
# - Perform basic data analysis
# - Analyze text content for common words

#%%
import pandas as pd
import glob
import re
from collections import Counter

print("Libraries imported successfully!")

#%% md
## 1. Load and Combine CSV Data

#%%
# Load all CSV files from data directory
csv_files = glob.glob("data/*.csv")
print(f"Found {len(csv_files)} CSV files")

# Combine all CSV files into one DataFrame
if csv_files:
    dataframes = [pd.read_csv(file) for file in csv_files]
    df = pd.concat(dataframes, ignore_index=True)
    print(f"Combined dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
else:
    print("No CSV files found in data/ directory")

#%% md
## 2. Data Overview and Basic Statistics

#%%
# Display basic information about the dataset
print("=== DATASET OVERVIEW ===")
print(df.info())
print("\n=== BASIC STATISTICS ===")
print(df.describe())

#%%
# Show first few rows
print("=== SAMPLE DATA ===")
df.head()

#%% md
## 3. Location-based Analysis (if location column exists)

#%%
if 'location' in df.columns:
    print("=== LOCATION ANALYSIS ===")
    
    # Count records per location
    location_counts = df.groupby('location').size()
    print("Records per location:")
    print(location_counts)
    
    # Sum numeric columns by location
    numeric_sums = df.groupby('location').sum(numeric_only=True)
    if not numeric_sums.empty:
        print("\nNumeric sums by location:")
        print(numeric_sums)
else:
    print("No 'location' column found in dataset")

#%% md
## 4. Text Analysis (if text column exists)

#%%
if 'text' in df.columns:
    print("=== TEXT ANALYSIS ===")
    
    # Define stop words to exclude from analysis
    stop_words = {
        'this', 'that', 'with', 'from', 'have', 'been', 'will', 'just', 'they',
        'their', 'what', 'when', 'where', 'which', 'while', 'about', 'after',
        'before', 'during', 'through', 'under', 'over', 'between', 'into',
        'than', 'then', 'there', 'here', 'were', 'would', 'could', 'should',
        'these', 'those', 'them', 'some', 'more', 'most', 'other', 'such',
        'only', 'also', 'very', 'much', 'many', 'like', 'https', 'http'
    }
    
    # Count meaningful words (4+ characters, excluding stop words)
    word_counts = Counter()
    
    for text in df['text'].dropna():
        words = re.findall(r'\b\w{4,}\b', text.lower())
        meaningful_words = [word for word in words if word not in stop_words]
        word_counts.update(meaningful_words)
    
    # Display top 20 most common words
    print("Top 20 most common meaningful words:")
    for word, count in word_counts.most_common(20):
        print(f"{word}: {count}")
else:
    print("No 'text' column found in dataset")

#%% md
## 5. Additional Analysis (Optional)
# Add any additional analysis cells here as needed

#%%
# Example: Display column-specific information
for col in df.columns:
    print(f"\n=== {col.upper()} COLUMN ===")
    print(f"Data type: {df[col].dtype}")
    print(f"Non-null values: {df[col].count()}/{len(df)}")
    
    if df[col].dtype == 'object':
        print(f"Unique values: {df[col].nunique()}")
        if df[col].nunique() < 20:  # Show unique values if not too many
            print(f"Values: {df[col].unique()}")
    else:
        print(f"Min: {df[col].min()}, Max: {df[col].max()}")

#%%
