#%% md
# This is a sample Jupyter Notebook

Below is an example of a code cell. 
Put your cursor into the cell and press <PERSON><PERSON>+En<PERSON> to execute it and select the next one, or click 'Run Cell' button.

Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

To learn more about Jupyter Notebooks in PyCharm, see [help](https://www.jetbrains.com/help/pycharm/ipython-notebook-support.html).
For an overview of PyCharm, go to Help -> Learn IDE features or refer to [our documentation](https://www.jetbrains.com/help/pycharm/getting-started.html).
#%%
print("Hello World!")

#%%
import pandas as pd
#%%
import glob

# Path where your CSVs are stored
path = "data/"  # change to your folder
all_files = glob.glob(path + "/*.csv")

dataframes = []

# Step 5: Loop through each CSV file and read it into pandas
for file in all_files:
    print(f"Loading file: {file}")   # Show which file is loading
    df = pd.read_csv(file)           # Read the CSV into a DataFrame
    dataframes.append(df)            # Add the DataFrame to our list

# Step 6: Concatenate all DataFrames into one
combined_df = pd.concat(dataframes, ignore_index=True)
#%%
combined_df
#%%
path = "data/"  # change to your folder
all_files = glob.glob(path + "/*.csv")

dataframes = []

# Step 5: Loop through each CSV file and read it into pandas
for file in all_files:
    # print(f"Loading file: {file}")   # Show which file is loading
    df = pd.read_csv(file)           # Read the CSV into a DataFrame
    dataframes.append(df)            # Add the DataFrame to our list

print("done")
# Step 6: Concatenate all DataFrames into one
combined_df = pd.concat(dataframes, ignore_index=True)
#%%
df
#%%
combined_df
#%%
df=[]
df
#%%
combined_df=[]

#%%
path = "data/"  # change to your folder
all_files = glob.glob(path + "/*.csv")

dataframes = []

# Step 5: Loop through each CSV file and read it into pandas
for file in all_files:
    # print(f"Loading file: {file}")   # Show which file is loading
    df = pd.read_csv(file)           # Read the CSV into a DataFrame
    dataframes.append(df)            # Add the DataFrame to our list

print("done")
# Step 6: Concatenate all DataFrames into one
combined_df = pd.concat(dataframes, ignore_index=True)

#%%
combined_df
#%%
combined_df.describe()

#%%
combined_df.groupby(by=['location'])
#%%
combined_df
#%%
grouped = combined_df.groupby("location")

#%%
grouped
#%%
print(grouped)
#%%
counts = grouped.size()
print("Counts per name:")
print(counts)

#%%
counts

#%%
sums = grouped.sum(numeric_only=True)
print("\nSum per name:")
print(sums)

#%%
sums
#%%
counts
#%%
combined_df.head(5)

#%%
combined_df['text'].head(5)
#%%
import re
from collections import Counter

# Tokenize and count words from the 'text' column
all_text = ' '.join(combined_df['text'].dropna())
words = re.findall(r'\b\w+\b', all_text.lower())
word_counts = Counter(words)

# Display top 10 most common words
print(word_counts.most_common(10))

#%%
import glob
import re
from collections import Counter
import pandas as pd

# Load all CSVs
files = glob.glob('data/*.csv')
dfs = [pd.read_csv(f) for f in files]
df = pd.concat(dfs, ignore_index=True)
#%%
import re
from collections import Counter

word_counts = Counter()

for text in df['text'].dropna():
    words = re.findall(r'\b\w{4,}\b', text.lower())
    word_counts.update(words)

# Display top 10 most common words
print(word_counts.most_common(10))

#%%
import re
from collections import Counter

# Define stop words to exclude
stop_words = {
    'this', 'that', 'with', 'from', 'have', 'been', 'will', 'just', 'they',
    'their', 'what', 'when', 'where', 'which', 'while', 'about', 'after',
    'before', 'during', 'through', 'under', 'over', 'between', 'into',
    'than', 'then', 'there', 'here', 'were', 'would', 'could', 'should',
    'these', 'those', 'them', 'some', 'more', 'most', 'other', 'such',
    'only', 'also', 'very', 'much', 'many', 'like', 'https', 'http'
}

word_counts = Counter()

for text in df['text'].dropna():
    words = re.findall(r'\b\w{4,}\b', text.lower())
    # Filter out stop words
    meaningful_words = [word for word in words if word not in stop_words]
    word_counts.update(meaningful_words)

# Display top 20 most common words
print(word_counts.most_common(20))

#%%
