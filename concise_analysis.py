#!/usr/bin/env python3
"""
Concise CSV Data Analysis Script
Combines CSV loading, data exploration, and text analysis in one clean script.
"""

import pandas as pd
import glob
import re
from collections import Counter


def load_csv_data(data_path="data/"):
    """Load and combine all CSV files from the specified directory."""
    csv_files = glob.glob(f"{data_path}/*.csv")
    
    if not csv_files:
        print(f"No CSV files found in {data_path}")
        return None
    
    print(f"Loading {len(csv_files)} CSV files...")
    dataframes = [pd.read_csv(file) for file in csv_files]
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"Combined dataset shape: {combined_df.shape}")
    
    return combined_df


def analyze_data(df):
    """Perform basic data analysis."""
    print("\n=== DATA OVERVIEW ===")
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    print("\n=== BASIC STATISTICS ===")
    print(df.describe())
    
    if 'location' in df.columns:
        print("\n=== LOCATION ANALYSIS ===")
        location_counts = df.groupby('location').size()
        print("Counts per location:")
        print(location_counts)
        
        # Numeric sums by location if numeric columns exist
        numeric_sums = df.groupby('location').sum(numeric_only=True)
        if not numeric_sums.empty:
            print("\nNumeric sums per location:")
            print(numeric_sums)


def analyze_text(df, text_column='text', min_word_length=4, top_n=20):
    """Analyze text content and find most common meaningful words."""
    if text_column not in df.columns:
        print(f"Column '{text_column}' not found in dataset")
        return
    
    # Define stop words to exclude
    stop_words = {
        'this', 'that', 'with', 'from', 'have', 'been', 'will', 'just', 'they',
        'their', 'what', 'when', 'where', 'which', 'while', 'about', 'after',
        'before', 'during', 'through', 'under', 'over', 'between', 'into',
        'than', 'then', 'there', 'here', 'were', 'would', 'could', 'should',
        'these', 'those', 'them', 'some', 'more', 'most', 'other', 'such',
        'only', 'also', 'very', 'much', 'many', 'like', 'https', 'http'
    }
    
    print(f"\n=== TEXT ANALYSIS ===")
    word_counts = Counter()
    
    # Process each text entry
    for text in df[text_column].dropna():
        # Extract words with minimum length
        words = re.findall(rf'\b\w{{{min_word_length},}}\b', text.lower())
        # Filter out stop words
        meaningful_words = [word for word in words if word not in stop_words]
        word_counts.update(meaningful_words)
    
    print(f"Top {top_n} most common meaningful words:")
    for word, count in word_counts.most_common(top_n):
        print(f"{word}: {count}")


def main():
    """Main execution function."""
    # Load data
    df = load_csv_data("data/")
    
    if df is None:
        return
    
    # Perform analyses
    analyze_data(df)
    analyze_text(df)
    
    # Display sample data
    print("\n=== SAMPLE DATA ===")
    print(df.head())


if __name__ == "__main__":
    main()
